import { NestFactory } from '@nestjs/core';
import { RideMatcherModule } from './ride-matcher.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';

async function bootstrap() {
  return await NestFactory.createMicroservice<MicroserviceOptions>(
    RideMatcherModule,
    {
      transport: Transport.RMQ,
      options: {
        urls: ['amqp://localhost:5672'],
        queue: 'cats_queue',
        queueOptions: {
          durable: false,
        },
      },
    },
  );
}
bootstrap()