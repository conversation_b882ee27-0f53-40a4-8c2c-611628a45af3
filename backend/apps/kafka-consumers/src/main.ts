import { NestFactory } from '@nestjs/core';
import { KafkaConsumersModule } from './kafka-consumers.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { Logger } from '@nestjs/common';
import { AppConfigService } from '@shared/shared';

async function bootstrap() {
  const logger = new Logger('KafkaConsumers');

  try {
    // Create application context to access configuration
    const appContext = await NestFactory.createApplicationContext(KafkaConsumersModule);
    const configService = appContext.get(AppConfigService);

    // Get configuration values
    const kafkaBrokers = configService.kafkaBrokers;
    const kafkaClientId = configService.kafkaClientId;
    const kafkaGroupId = configService.kafkaGroupId;
    const kafkaRetryAttempts = configService.kafkaRetryAttempts;
    const kafkaRetryDelay = configService.kafkaRetryDelay;

    logger.log(`Starting Kafka Consumer microservice...`);
    logger.log(`Kafka Brokers: ${kafkaBrokers.join(', ')}`);
    logger.log(`Client ID: ${kafkaClientId}`);
    logger.log(`Group ID: ${kafkaGroupId}`);

    // Create microservice with proper configuration
    const app = await NestFactory.createMicroservice<MicroserviceOptions>(
      KafkaConsumersModule,
      {
        transport: Transport.KAFKA,
        options: {
          client: {
            clientId: kafkaClientId,
            brokers: kafkaBrokers,
            retry: {
              retries: kafkaRetryAttempts,
              initialRetryTime: kafkaRetryDelay,
              maxRetryTime: kafkaRetryDelay * 10,
            },
            connectionTimeout: 10000,
            requestTimeout: 30000,
          },
          consumer: {
            groupId: kafkaGroupId,
            allowAutoTopicCreation: false,
            sessionTimeout: 30000,
            heartbeatInterval: 3000,
            maxBytesPerPartition: 1048576, // 1MB
            retry: {
              retries: kafkaRetryAttempts,
            },
          },
          run: {
            autoCommit: true,
            autoCommitInterval: 5000,
            autoCommitThreshold: 100,
          },
        },
      },
    );

    // Enable graceful shutdown
    app.enableShutdownHooks();

    // Setup graceful shutdown handlers
    process.on('SIGTERM', async () => {
      logger.log('SIGTERM received, shutting down gracefully...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('SIGINT received, shutting down gracefully...');
      await app.close();
      process.exit(0);
    });

    // Start the microservice
    await app.listen();
    logger.log('Kafka Consumer microservice is listening for messages');

    // Close the application context
    await appContext.close();

  } catch (error) {
    logger.error('Failed to start Kafka Consumer microservice', error);
    process.exit(1);
  }
}

bootstrap().catch((error) => {
  console.error('Bootstrap failed:', error);
  process.exit(1);
});
