import { NestFactory } from '@nestjs/core';
import { KafkaConsumersModule } from './kafka-consumers.module';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';

async function bootstrap() {
  await NestFactory.createMicroservice<MicroserviceOptions>(
    KafkaConsumersModule,
    {
      transport: Transport.KAFKA,
      options: {
        client: {
          brokers: ['localhost:9092'],
        },
      },
    },
  );
}
bootstrap();
