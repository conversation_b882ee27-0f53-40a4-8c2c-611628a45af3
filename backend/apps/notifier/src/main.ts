import { NestFactory } from '@nestjs/core';
import { NotifierModule } from './notifier.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { AppConfigService } from '@shared/shared';
import helmet from 'helmet';

async function bootstrap() {
  const logger = new Logger('Notifier');

  try {
    logger.log('Starting Notifier microservice...');

    // Create the application
    const app = await NestFactory.create(NotifierModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Get configuration service
    const configService = app.get(AppConfigService);

    // Get configuration values
    const port = configService.notifierPort;
    const host = configService.coreApiHost;
    const corsOrigins = configService.corsOrigins;
    const shutdownTimeout = configService.shutdownTimeout;

    logger.log(`Port: ${port}`);
    logger.log(`Host: ${host}`);
    logger.log(`Environment: ${configService.nodeEnv}`);

    // Security middleware
    app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false,
    }));

    // CORS configuration
    app.enableCors({
      origin: corsOrigins === '*' ? true : corsOrigins.split(',').map(origin => origin.trim()),
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
      credentials: true,
    });

    // Global validation pipe
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
          enableImplicitConversion: true,
        },
      }),
    );

    // Enable graceful shutdown
    app.enableShutdownHooks();

    // Setup graceful shutdown handlers
    const gracefulShutdown = async (signal: string) => {
      logger.log(`${signal} received, shutting down gracefully...`);

      const shutdownTimer = setTimeout(() => {
        logger.error('Forced shutdown due to timeout');
        process.exit(1);
      }, shutdownTimeout);

      try {
        await app.close();
        clearTimeout(shutdownTimer);
        logger.log('Notifier microservice shut down successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        clearTimeout(shutdownTimer);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });

    // Start the server
    await app.listen(port, host);
    logger.log(`Notifier microservice is running on http://${host}:${port}`);

  } catch (error) {
    logger.error('Failed to start Notifier microservice:', error);
    process.exit(1);
  }
}

bootstrap().catch((error) => {
  console.error('Bootstrap failed:', error);
  process.exit(1);
});
